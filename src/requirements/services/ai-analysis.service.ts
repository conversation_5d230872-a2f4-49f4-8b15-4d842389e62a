import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface AiAnalysisResult {
  qualityScore: number;
  feedback: {
    clarity: { score: number; feedback: string; suggestions?: string[] };
    completeness: { score: number; feedback: string; suggestions?: string[] };
    consistency: { score: number; feedback: string; suggestions?: string[] };
    testability: { score: number; feedback: string; suggestions?: string[] };
    feasibility: { score: number; feedback: string; suggestions?: string[] };
    businessValue: { score: number; feedback: string; suggestions?: string[] };
  };
  overallFeedback: string;
  criticalIssues: string[];
  recommendations: string[];
  tokensUsed: number;
  model: string;
  provider: string;
  cost?: number;
}

@Injectable()
export class AiAnalysisService {
  private readonly logger = new Logger(AiAnalysisService.name);
  private readonly llmProvider: string;

  constructor(private configService: ConfigService) {
    this.llmProvider = this.configService.get<string>('LLM', 'OPENAI');
  }

  async analyzeRequirement(
    content: string,
    context: string,
    embeddingVector: number[],
    requirementId: string
  ): Promise<AiAnalysisResult> {
    this.logger.log(`Starting AI analysis for requirement ${requirementId} using ${this.llmProvider}`);

    try {
      if (this.llmProvider === 'GEMINI') {
        return await this.analyzeWithGemini(content, context, embeddingVector, requirementId);
      } else {
        return await this.analyzeWithOpenAI(content, context, embeddingVector, requirementId);
      }
    } catch (error) {
      this.logger.error(`Error analyzing requirement ${requirementId}:`, error);
      throw error;
    }
  }

  private async analyzeWithGemini(
    content: string,
    context: string,
    embeddingVector: number[],
    requirementId: string
  ): Promise<AiAnalysisResult> {
    const { GoogleGenerativeAI } = require('@google/generative-ai');
    
    const genAI = new GoogleGenerativeAI(this.configService.get<string>('GEMINI_API_KEY'));
    const model = genAI.getGenerativeModel({ model: 'gemini-2.5-flash' });

    const analysisPrompt = this.buildAnalysisPrompt(content, context);

    const result = await model.generateContent(analysisPrompt);
    const response = await result.response;
    const analysisText = response.text();

    // Clean the response - remove markdown code blocks if present
    const cleanContent = analysisText.replace(/```json\s*|\s*```/g, '').trim();
    const analysis = JSON.parse(cleanContent);

    const tokensUsed = Math.round(analysisText.length / 4); // Rough estimation
    const costPer1kTokens = 0.000125; // Gemini pricing estimate
    const cost = (tokensUsed / 1000) * costPer1kTokens;

    return {
      qualityScore: analysis.qualityScore,
      feedback: analysis.feedback,
      overallFeedback: analysis.overallFeedback,
      criticalIssues: analysis.criticalIssues || [],
      recommendations: analysis.recommendations || [],
      tokensUsed,
      model: 'gemini-2.5-flash',
      provider: 'gemini',
      cost
    };
  }

  private async analyzeWithOpenAI(
    content: string,
    context: string,
    embeddingVector: number[],
    requirementId: string
  ): Promise<AiAnalysisResult> {
    const { OpenAI } = require('openai');
    
    const openai = new OpenAI({
      apiKey: this.configService.get<string>('OPENAI_API_KEY'),
    });

    const analysisPrompt = this.buildAnalysisPrompt(content, context);

    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: 'You are an expert business analyst and QA specialist. Analyze requirements for quality, clarity, and completeness. Always respond with valid JSON.'
        },
        {
          role: 'user',
          content: analysisPrompt
        }
      ],
      temperature: 0.3,
      max_tokens: 2000,
    });

    const analysisText = response.choices[0].message.content;
    const analysis = JSON.parse(analysisText);

    const tokensUsed = response.usage?.total_tokens || 0;
    const costPer1kTokens = 0.03; // GPT-4 pricing estimate
    const cost = (tokensUsed / 1000) * costPer1kTokens;

    return {
      qualityScore: analysis.qualityScore,
      feedback: analysis.feedback,
      overallFeedback: analysis.overallFeedback,
      criticalIssues: analysis.criticalIssues || [],
      recommendations: analysis.recommendations || [],
      tokensUsed,
      model: 'gpt-4',
      provider: 'openai',
      cost
    };
  }

  private buildAnalysisPrompt(content: string, context: string): string {
    // Detect if this is image-heavy content
    const hasLLMAnalysis = context.includes('=== LLM ANALYSIS ===');
    const hasVisualElements = context.includes('📷') || context.includes('📊') || context.includes('🔍');

    return `
Analyze the following business requirement for quality, clarity, and potential issues.

${hasLLMAnalysis ? 'NOTE: This requirement contains visual elements (images, diagrams, UI mockups) that have been analyzed by LLM.' : ''}

ORIGINAL CONTENT:
${content}

ENHANCED CONTEXT (includes LLM analysis of images/diagrams if present):
${context || 'No additional context available'}

${hasVisualElements ? `
SPECIAL INSTRUCTIONS FOR VISUAL CONTENT:
- Pay special attention to UI elements, login forms, interface designs
- Consider usability and user experience aspects
- Evaluate if visual mockups align with functional requirements
- Check for accessibility considerations in UI designs
- Assess if the visual elements provide clear user guidance
` : ''}

Please provide a comprehensive analysis in the following JSON format:

{
  "qualityScore": <number 1-100>,
  "feedback": {
    "clarity": {
      "score": <number 1-10>,
      "feedback": "<detailed feedback>",
      "suggestions": ["<suggestion 1>", "<suggestion 2>"]
    },
    "completeness": {
      "score": <number 1-10>,
      "feedback": "<detailed feedback>",
      "suggestions": ["<suggestion 1>", "<suggestion 2>"]
    },
    "consistency": {
      "score": <number 1-10>,
      "feedback": "<detailed feedback>",
      "suggestions": ["<suggestion 1>", "<suggestion 2>"]
    },
    "testability": {
      "score": <number 1-10>,
      "feedback": "<detailed feedback>",
      "suggestions": ["<suggestion 1>", "<suggestion 2>"]
    },
    "feasibility": {
      "score": <number 1-10>,
      "feedback": "<detailed feedback>",
      "suggestions": ["<suggestion 1>", "<suggestion 2>"]
    },
    "businessValue": {
      "score": <number 1-10>,
      "feedback": "<detailed feedback>",
      "suggestions": ["<suggestion 1>", "<suggestion 2>"]
    }
  },
  "overallFeedback": "<comprehensive summary>",
  "criticalIssues": ["<critical issue 1>", "<critical issue 2>"],
  "recommendations": ["<recommendation 1>", "<recommendation 2>"]
}

Focus on:
1. Clarity: Are requirements clearly stated and unambiguous?
2. Completeness: Are all necessary details provided?
3. Consistency: Are terms and concepts used consistently?
4. Testability: Can these requirements be tested and verified?
5. Feasibility: Are the requirements technically and practically achievable?
6. Business Value: Do the requirements align with business objectives?

Identify any critical issues that could lead to development problems or user dissatisfaction.
`;
  }
}
